"""
GMGN钱包统计数据爬虫

本模块实现了从GMGN平台获取钱包统计数据的爬虫功能。
继承BasicSpider基类，具备代理管理和重试机制。
"""

import asyncio
import json
import random
import time
import uuid
from typing import Dict, Optional, Any
import logging

from utils.spiders.smart_money import BasicSpider
from models.gmgn_wallet_stats import GmgnWalletStats
from curl_cffi.requests import Response


class GmgnWalletStatsSpider(BasicSpider):
    """
    GMGN钱包统计数据爬虫
    
    负责获取钱包的交易统计数据，包括胜率、收益率、风险指标等信息。
    支持多种时间窗口（all/7d/1d）的数据获取。
    """
    
    def __init__(self, max_retries: int = 5, retry_interval: float = 2.0):
        """
        初始化爬虫
        
        Args:
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间(秒)
        """
        super().__init__(max_retries, retry_interval)
        self.base_url = "https://gmgn.ai/api/v1/wallet_stat/sol/{}/{}"
        self.headers = {
            'referer': 'https://gmgn.ai/sol/address/dDO6ZdYE_AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
        
    async def setup(self):
        """
        设置爬虫会话和请求头
        
        生成随机的设备标识，配置GMGN API特定的请求头
        """
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
        
    async def get_wallet_stats(
        self, 
        wallet_address: str, 
        period: str = "all"
    ) -> Optional[GmgnWalletStats]:
        """
        获取单个钱包的统计数据
        
        Args:
            wallet_address: 钱包地址
            period: 时间窗口，支持 "all", "7d", "1d"
            
        Returns:
            GmgnWalletStats对象或None（失败时）
        """
        if not wallet_address:
            self.logger.error("钱包地址不能为空")
            return None
            
        try:
            # 构建请求URL (包含period)
            url = self.base_url.format(wallet_address, period)
            
            params = {
                "period": period,
                "device_id": "65990bc3-0a13-4d99-8c66-eeea94916b0f",
                "client_id": "gmgn_web_20250603-1799-730df90",
                "from_app": "gmgn",
                "app_ver": "20250603-1799-730df90",
                "tz_name": "Asia/Shanghai",
                "tz_offset": "28800",
                "app_lang": "zh-CN",
            }
            
            self.logger.info(f"开始获取钱包 {wallet_address} 的统计数据 (url: {url}) (period: {period}), params: {params}")
            
            # 发送请求
            response = await self.get(
                url,
                params=params
            )
            
            # 检查响应状态
            if response.status_code != 200:
                self.logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return None
            
            # 解析响应数据
            response_data = response.json()
            
            # 验证响应格式
            if not self._validate_response(response_data):
                self.logger.error(f"API响应格式无效: {response_data}")
                return None
            
            # 解析并创建数据模型
            stats_data = self._parse_wallet_data(wallet_address, response_data, period)
            
            self.logger.info(f"成功获取钱包 {wallet_address} 的统计数据")
            return stats_data
            
        except Exception as e:
            self.logger.error(f"获取钱包 {wallet_address} 统计数据失败: {e}")
            return None
    
    def _validate_response(self, response_data: dict) -> bool:
        """验证API响应的基本结构和成功状态"""
        if not isinstance(response_data, dict):
            self.logger.error(f"API响应不是有效的JSON对象: {response_data}")
            return False
        
        # 检查必需的顶级字段
        required_fields = ["code", "message", "data"]
        for field in required_fields:
            if field not in response_data:
                self.logger.error(f"响应缺少必需字段: {field}, response_data: {response_data}")
                return False
        
        # 检查成功状态
        if response_data.get("code") != 0:
            self.logger.error(f"API返回错误: {response_data.get('message', '未知错误')}")
            return False
            
        # 检查 data 字段是否为字典
        if not isinstance(response_data.get("data"), dict):
            self.logger.error(f"API响应的 'data' 字段不是有效的对象: {response_data.get('data')}")
            return False
            
        return True
    
    def _parse_wallet_data(self, wallet_address: str, response_data: dict, period: str) -> Optional[GmgnWalletStats]:
        """解析API响应数据并创建GmgnWalletStats实例
        
        Args:
            wallet_address: 钱包地址
            response_data: API的完整响应字典
            period: 请求的时间窗口 (e.g., "all", "7d")
            
        Returns:
            GmgnWalletStats: 解析后的数据模型实例，如果解析失败则返回None
        """
        if not self._validate_response(response_data):
            return None

        # 实际的统计数据在 'data' 字段中
        stats_api_data = response_data.get("data")

        try:
            # 将API数据和钱包地址、周期传递给模型工厂方法
            # 模型工厂方法将处理字段映射和类型转换
            stats = GmgnWalletStats.create_from_api_data(
                wallet_address=wallet_address, 
                api_data=stats_api_data, 
                period=period
            )
            return stats
        except Exception as e:
            import traceback
            self.logger.error(f"解析钱包 {wallet_address} 数据时出错 ({type(e).__name__}: {e})")
            self.logger.error(f"完整错误堆栈: {traceback.format_exc()}")
            self.logger.error(f"API响应数据 (传递给create_from_api_data的部分): {stats_api_data}")
            return None
    
    async def get_multiple_periods_stats(
        self, 
        wallet_address: str, 
        periods: list = None
    ) -> Dict[str, Optional[GmgnWalletStats]]:
        """
        获取钱包多个时间窗口的统计数据
        
        Args:
            wallet_address: 钱包地址
            periods: 时间窗口列表，默认为["all", "7d", "1d"]
            
        Returns:
            时间窗口到GmgnWalletStats的映射字典
        """
        if periods is None:
            periods = ["all", "7d", "1d"]
        
        results = {}
        
        for period in periods:
            try:
                stats = await self.get_wallet_stats(wallet_address, period)
                results[period] = stats
                
                # 添加间隔，避免请求过快
                if len(periods) > 1:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"获取钱包 {wallet_address} period {period} 数据失败: {e}")
                results[period] = None
        
        return results 
    

if __name__ == "__main__":
    # 配置日志系统
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # 输出到控制台
        ]
    )
    
    async def main():
        # 初始化数据库连接
        from models import init_db
        await init_db()
        
        spider = GmgnWalletStatsSpider()
        await spider.setup()
        result = await spider.get_wallet_stats("AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW")
        if result:
            print("成功创建钱包统计对象:")
            print(f"钱包地址: {result.wallet_address}")
            print(f"总收益率: {result.pnl}")
            print(f"胜率: {result.winrate}")
            print(f"交易次数: {result.trade_frequency_total}")
        else:
            print("获取钱包统计失败")
    asyncio.run(main())
 