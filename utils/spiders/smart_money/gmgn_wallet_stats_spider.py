"""
GMGN钱包统计数据爬虫

本模块实现了从GMGN平台获取钱包统计数据的爬虫功能。
继承BasicSpider基类，具备代理管理和重试机制。
"""

import asyncio
import json
import random
import time
import uuid
from typing import Dict, Optional, Any
import logging

from utils.spiders.smart_money import BasicSpider
from models.gmgn_wallet_stats import GmgnWalletStats
from curl_cffi.requests import Response


class GmgnWalletStatsSpider(BasicSpider):
    """
    GMGN钱包统计数据爬虫
    
    负责获取钱包的交易统计数据，包括胜率、收益率、风险指标等信息。
    支持多种时间窗口（all/7d/1d）的数据获取。
    """
    
    def __init__(self, max_retries: int = 5, retry_interval: float = 2.0):
        """
        初始化爬虫

        Args:
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间(秒)
        """
        super().__init__(max_retries, retry_interval)
        self.base_url = "https://gmgn.ai/api/v1/wallet_stat/sol/{}/{}"
        # 使用与成功爬虫相同的完整请求头
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/address/dDO6ZdYE_AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
    async def setup(self):
        """
        设置爬虫会话和请求头

        生成随机的设备标识，配置GMGN API特定的请求头
        """
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置更新的浏览器模拟，使用更新的版本
        self.session.impersonate = "chrome120"  # 使用更新的Chrome版本

        # 设置额外的会话参数来绕过检测
        if hasattr(self.session, 'verify'):
            self.session.verify = False  # 禁用SSL验证可能有助于绕过某些检测

        # 设置超时
        self.session.timeout = 30

    async def _handle_cloudflare_challenge(self, response, url, params):
        """
        处理 Cloudflare 挑战

        Args:
            response: 包含 Cloudflare 挑战的响应
            url: 原始请求URL
            params: 原始请求参数

        Returns:
            处理后的响应或None
        """
        if response.status_code == 403 and "Just a moment" in response.text:
            self.logger.warning("检测到 Cloudflare 挑战，尝试等待并重试...")

            # 等待更长时间，让 Cloudflare 验证通过
            await asyncio.sleep(random.uniform(5, 10))

            # 切换到新的代理和会话
            await self.switch_session()
            await self.setup()

            # 添加更多真实的浏览器行为
            # 首先访问主页来建立会话
            try:
                home_response = await self.session.get("https://gmgn.ai/")
                if home_response.status_code == 200:
                    self.logger.info("成功访问主页，建立会话")
                    # 等待一段时间模拟用户浏览
                    await asyncio.sleep(random.uniform(2, 5))
                else:
                    self.logger.warning(f"访问主页失败: {home_response.status_code}")
            except Exception as e:
                self.logger.warning(f"访问主页时出错: {e}")

            # 重新尝试原始请求
            try:
                new_response = await self.session.get(url, params=params)
                return new_response
            except Exception as e:
                self.logger.error(f"重试请求失败: {e}")
                return None

        return response

    async def get_wallet_stats(
        self, 
        wallet_address: str, 
        period: str = "all"
    ) -> Optional[GmgnWalletStats]:
        """
        获取单个钱包的统计数据
        
        Args:
            wallet_address: 钱包地址
            period: 时间窗口，支持 "all", "7d", "1d"
            
        Returns:
            GmgnWalletStats对象或None（失败时）
        """
        if not wallet_address:
            self.logger.error("钱包地址不能为空")
            return None
            
        try:
            # 构建请求URL (包含period)
            url = self.base_url.format(wallet_address, period)
            
            # 生成随机设备ID，避免被识别为机器人
            device_id = str(uuid.uuid4())

            params = {
                "period": period,
                "device_id": device_id,
                # 使用与其他成功爬虫相同的版本号
                "client_id": "gmgn_web_2025.0307.143957",
                "from_app": "gmgn",
                "app_ver": "2025.0307.143957",
                "tz_name": "Asia/Shanghai",
                "tz_offset": "28800",
                "app_lang": "zh-CN",
            }
            
            self.logger.info(f"开始获取钱包 {wallet_address} 的统计数据 (url: {url}) (period: {period})")
            self.logger.debug(f"请求参数: {params}")

            # 添加随机延迟，模拟人类行为
            await asyncio.sleep(random.uniform(0.5, 1.5))

            # 发送请求
            response = await self.get(
                url,
                params=params
            )

            # 记录响应状态和内容
            self.logger.debug(f"API响应状态码: {response.status_code}")
            self.logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符

            # 检查响应状态
            if response.status_code != 200:
                self.logger.error(f"API请求失败，状态码: {response.status_code}")
                self.logger.error(f"错误响应: {response.text}")
                return None
            
            # 解析响应数据
            try:
                response_data = response.json()
                self.logger.debug(f"解析后的API响应: {response_data}")
            except json.JSONDecodeError as e:
                self.logger.error(f"解析API响应JSON失败: {str(e)}")
                self.logger.error(f"原始响应: {response.text}")
                return None

            # 验证响应格式
            if not self._validate_response(response_data):
                self.logger.error(f"API响应格式无效: {response_data}")
                return None

            # 解析并创建数据模型
            stats_data = self._parse_wallet_data(wallet_address, response_data, period)

            self.logger.info(f"成功获取钱包 {wallet_address} 的统计数据")
            return stats_data

        except Exception as e:
            self.logger.error(f"获取钱包 {wallet_address} 统计数据失败: {str(e)}")
            import traceback
            self.logger.error(f"完整错误堆栈: {traceback.format_exc()}")
            return None
    
    def _validate_response(self, response_data: dict) -> bool:
        """验证API响应的基本结构和成功状态"""
        if not isinstance(response_data, dict):
            self.logger.error(f"API响应不是有效的JSON对象: {response_data}")
            return False
        
        # 检查必需的顶级字段
        required_fields = ["code", "message", "data"]
        for field in required_fields:
            if field not in response_data:
                self.logger.error(f"响应缺少必需字段: {field}, response_data: {response_data}")
                return False
        
        # 检查成功状态
        if response_data.get("code") != 0:
            self.logger.error(f"API返回错误: {response_data.get('message', '未知错误')}")
            return False
            
        # 检查 data 字段是否为字典
        if not isinstance(response_data.get("data"), dict):
            self.logger.error(f"API响应的 'data' 字段不是有效的对象: {response_data.get('data')}")
            return False
            
        return True
    
    def _parse_wallet_data(self, wallet_address: str, response_data: dict, period: str) -> Optional[GmgnWalletStats]:
        """解析API响应数据并创建GmgnWalletStats实例
        
        Args:
            wallet_address: 钱包地址
            response_data: API的完整响应字典
            period: 请求的时间窗口 (e.g., "all", "7d")
            
        Returns:
            GmgnWalletStats: 解析后的数据模型实例，如果解析失败则返回None
        """
        if not self._validate_response(response_data):
            return None

        # 实际的统计数据在 'data' 字段中
        stats_api_data = response_data.get("data")

        try:
            # 将API数据和钱包地址、周期传递给模型工厂方法
            # 模型工厂方法将处理字段映射和类型转换
            stats = GmgnWalletStats.create_from_api_data(
                wallet_address=wallet_address, 
                api_data=stats_api_data, 
                period=period
            )
            return stats
        except Exception as e:
            import traceback
            self.logger.error(f"解析钱包 {wallet_address} 数据时出错 ({type(e).__name__}: {e})")
            self.logger.error(f"完整错误堆栈: {traceback.format_exc()}")
            self.logger.error(f"API响应数据 (传递给create_from_api_data的部分): {stats_api_data}")
            return None
    
    async def get_multiple_periods_stats(
        self, 
        wallet_address: str, 
        periods: list = None
    ) -> Dict[str, Optional[GmgnWalletStats]]:
        """
        获取钱包多个时间窗口的统计数据
        
        Args:
            wallet_address: 钱包地址
            periods: 时间窗口列表，默认为["all", "7d", "1d"]
            
        Returns:
            时间窗口到GmgnWalletStats的映射字典
        """
        if periods is None:
            periods = ["all", "7d", "1d"]
        
        results = {}
        
        for i, period in enumerate(periods):
            try:
                stats = await self.get_wallet_stats(wallet_address, period)
                results[period] = stats

                # 添加间隔，避免请求过快，除了最后一个请求
                if i < len(periods) - 1:
                    # 随机间隔，模拟人类行为
                    interval = random.uniform(1.0, 2.5)
                    self.logger.debug(f"等待 {interval:.2f} 秒后获取下一个时间窗口数据...")
                    await asyncio.sleep(interval)

            except Exception as e:
                self.logger.error(f"获取钱包 {wallet_address} period {period} 数据失败: {e}")
                import traceback
                self.logger.error(f"完整错误堆栈: {traceback.format_exc()}")
                results[period] = None
        
        return results 
    

if __name__ == "__main__":
    # 配置日志系统
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # 输出到控制台
        ]
    )
    
    async def main():
        # 初始化数据库连接
        from models import init_db
        await init_db()
        
        spider = GmgnWalletStatsSpider()
        await spider.setup()
        result = await spider.get_wallet_stats("AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW")
        if result:
            print("成功创建钱包统计对象:")
            print(f"钱包地址: {result.wallet_address}")
            print(f"总收益率: {result.pnl}")
            print(f"胜率: {result.winrate}")
            print(f"交易次数: {result.trade_frequency_total}")
        else:
            print("获取钱包统计失败")
    asyncio.run(main())
 