name: "GMGN KOL钱包统计数据更新"
description: "定期获取和更新KOL钱包的统计数据到独立数据表，支持多时间窗口数据存储"

nodes:
  - name: "WalletStatsSchedulerNode"
    node_type: "input"
    interval: 1800  # 30分钟执行一次
    generate_data: workflows.gmgn_kol_wallet_stats.handler.generate_wallet_addresses
    flow_control:
      max_pending_messages: 50  # 最大等待处理的消息数
      check_interval: 5         # 流量控制检查间隔(秒)
      enable_flow_control: true # 启用流量控制

  - name: "WalletStatsProcessNode"
    node_type: "process"
    depend_ons: ["WalletStatsSchedulerNode"]
    concurrency: 1   # 1个并发工作者
    interval: 1      # 每个请求间隔1秒，避免API限流
    process_item: workflows.gmgn_kol_wallet_stats.handler.process_wallet_stats

  - name: "WalletStatsStoreNode"
    node_type: "storage"
    depend_ons: ["WalletStatsProcessNode"]
    batch_size: 100  # 每批处理100条数据，优化数据库性能
    store_data: workflows.gmgn_kol_wallet_stats.handler.store_wallet_stats
    validate: workflows.gmgn_kol_wallet_stats.handler.validate_wallet_data 