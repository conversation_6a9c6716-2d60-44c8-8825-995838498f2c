"""
GMGN KOL钱包统计数据工作流处理器单元测试

本模块测试工作流处理器的各项功能，包括：
- 钱包地址列表生成
- 钱包统计数据处理
- 批量数据存储
- 数据验证功能
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from workflows.gmgn_kol_wallet_stats.handler import (
    generate_wallet_addresses,
    process_wallet_stats,
    store_wallet_stats,
    validate_wallet_data,
    get_wallet_stats_multiple_periods
)


class TestGmgnKolWalletStatsHandler:
    """工作流处理器测试类"""
    
    @pytest.fixture
    def mock_wallet_stats(self):
        """创建模拟的钱包统计数据字典"""
        # 返回字典而不是Beanie模型实例，避免Beanie初始化问题
        return {
            "wallet_address": "test_wallet_123",
            "period": "all",
            "chain": "sol",
            "buy": 10,
            "sell": 8,
            "total_count": 18,
            "win_rate": 0.6,
            "realized_pnl": 1500.50,
            "realized_pnl_7d": 300.25,
            "unrealized_pnl": 200.75,
            "total_profit": 1701.25,
            "avg_buy_price": 50.5,
            "avg_sell_price": 75.8,
            "last_active": datetime(2024, 1, 1, 12, 0, 0),
            "risk": {
                "avg_cost": 25.5,
                "avg_sold": 45.8,
                "total_cost": 255.0,
                "avg_holding_period": 2.5
            }
        }
    
    @pytest.fixture
    def valid_wallet_data(self):
        """创建有效的钱包数据字典"""
        return {
            "wallet_address": "test_wallet_123",
            "period": "all",
            "chain": "sol",
            "buy": 10,
            "sell": 8,
            "total_count": 18,
            "win_rate": 0.6,
            "realized_pnl": 1500.50,
            "data_timestamp": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.gmgn_wallet_stats_dao')
    async def test_generate_wallet_addresses_success(self, mock_dao):
        """测试用例 1.1: 成功生成钱包地址列表"""
        # Mock DAO返回
        mock_addresses = ["wallet1", "wallet2", "wallet3"]
        mock_dao.find_wallets_need_stats_update.return_value = mock_addresses
        
        result = await generate_wallet_addresses()
        
        # 验证结果
        assert result == mock_addresses
        
        # 验证调用参数
        mock_dao.find_wallets_need_stats_update.assert_called_once_with(
            limit=50,
            hours_threshold=1,
            period="all"
        )
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.gmgn_wallet_stats_dao')
    async def test_generate_wallet_addresses_empty(self, mock_dao):
        """测试用例 1.2: 没有待更新的钱包地址"""
        # Mock DAO返回空列表
        mock_dao.find_wallets_need_stats_update.return_value = []
        
        result = await generate_wallet_addresses()
        
        # 验证结果
        assert result == []
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.gmgn_wallet_stats_dao')
    async def test_generate_wallet_addresses_exception(self, mock_dao):
        """测试用例 1.3: DAO异常处理"""
        # Mock DAO抛出异常
        mock_dao.find_wallets_need_stats_update.side_effect = Exception("Database error")
        
        result = await generate_wallet_addresses()
        
        # 验证返回空列表
        assert result == []
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.GmgnWalletStatsSpider')
    async def test_process_wallet_stats_success(self, mock_spider_class, mock_wallet_stats):
        """测试用例 2.1: 成功处理钱包统计数据"""
        # Mock爬虫实例
        mock_spider = AsyncMock()
        mock_spider.get_wallet_stats.return_value = mock_wallet_stats
        mock_spider.close.return_value = None
        mock_spider_class.return_value = mock_spider
        
        result = await process_wallet_stats("test_wallet_123")
        
        # 验证结果
        assert result is not None
        assert result["wallet_address"] == "test_wallet_123"
        assert result["period"] == "all"
        assert result["chain"] == "sol"
        assert result["buy"] == 10
        assert result["sell"] == 8
        assert result["risk"]["avg_cost"] == 25.5
        
        # 验证调用
        mock_spider.get_wallet_stats.assert_called_once_with("test_wallet_123", period="all")
        mock_spider.close.assert_called_once()
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.GmgnWalletStatsSpider')
    async def test_process_wallet_stats_no_data(self, mock_spider_class):
        """测试用例 2.2: 爬虫返回空数据"""
        # Mock爬虫返回None
        mock_spider = AsyncMock()
        mock_spider.get_wallet_stats.return_value = None
        mock_spider.close.return_value = None
        mock_spider_class.return_value = mock_spider
        
        result = await process_wallet_stats("test_wallet_123")
        
        # 验证返回None
        assert result is None
    
    async def test_process_wallet_stats_empty_address(self):
        """测试用例 2.3: 空钱包地址处理"""
        result = await process_wallet_stats("")
        assert result is None
        
        result = await process_wallet_stats(None)
        assert result is None
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.GmgnWalletStatsSpider')
    async def test_process_wallet_stats_exception(self, mock_spider_class):
        """测试用例 2.4: 处理过程中异常"""
        # Mock爬虫抛出异常
        mock_spider = AsyncMock()
        mock_spider.get_wallet_stats.side_effect = Exception("Network error")
        mock_spider.close.return_value = None
        mock_spider_class.return_value = mock_spider
        
        result = await process_wallet_stats("test_wallet_123")
        
        # 验证返回None
        assert result is None
        
        # 验证清理方法被调用
        mock_spider.close.assert_called_once()
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.validate_wallet_data')
    @patch('workflows.gmgn_kol_wallet_stats.handler.gmgn_wallet_stats_dao')
    async def test_store_wallet_stats_success(self, mock_dao, mock_validate, valid_wallet_data):
        """测试用例 3.1: 成功批量存储数据"""
        # Mock验证通过
        mock_validate.return_value = True
        
        # Mock DAO返回
        mock_dao.batch_upsert_wallet_stats.return_value = {
            "success_count": 2,
            "error_count": 0,
            "errors": []
        }
        
        data = [valid_wallet_data, valid_wallet_data.copy()]
        result = await store_wallet_stats(data)
        
        # 验证结果
        assert result == 2
        
        # 验证调用
        mock_dao.batch_upsert_wallet_stats.assert_called_once()
        call_args = mock_dao.batch_upsert_wallet_stats.call_args
        assert call_args[0][1] == "all"  # period参数
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.validate_wallet_data')
    async def test_store_wallet_stats_empty_data(self, mock_validate):
        """测试用例 3.2: 空数据处理"""
        result = await store_wallet_stats([])
        assert result == 0
        
        result = await store_wallet_stats(None)
        assert result == 0
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.validate_wallet_data')
    @patch('workflows.gmgn_kol_wallet_stats.handler.gmgn_wallet_stats_dao')
    async def test_store_wallet_stats_validation_failure(self, mock_dao, mock_validate, valid_wallet_data):
        """测试用例 3.3: 数据验证失败"""
        # Mock验证失败
        mock_validate.return_value = False
        
        data = [valid_wallet_data]
        result = await store_wallet_stats(data)
        
        # 验证结果为0，DAO未被调用
        assert result == 0
        mock_dao.batch_upsert_wallet_stats.assert_not_called()
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.validate_wallet_data')
    @patch('workflows.gmgn_kol_wallet_stats.handler.gmgn_wallet_stats_dao')
    async def test_store_wallet_stats_partial_errors(self, mock_dao, mock_validate, valid_wallet_data):
        """测试用例 3.4: 部分存储失败"""
        # Mock验证通过
        mock_validate.return_value = True
        
        # Mock DAO返回部分错误
        mock_dao.batch_upsert_wallet_stats.return_value = {
            "success_count": 1,
            "error_count": 1,
            "errors": ["Wallet validation failed"]
        }
        
        data = [valid_wallet_data, valid_wallet_data.copy()]
        result = await store_wallet_stats(data)
        
        # 验证结果
        assert result == 1
    
    def test_validate_wallet_data_valid(self, valid_wallet_data):
        """测试用例 4.1: 有效数据验证"""
        result = validate_wallet_data(valid_wallet_data)
        assert result is True
    
    def test_validate_wallet_data_missing_required_fields(self):
        """测试用例 4.2: 缺少必需字段"""
        # 缺少wallet_address
        invalid_data = {
            "period": "all",
            "chain": "sol"
        }
        result = validate_wallet_data(invalid_data)
        assert result is False
        
        # 缺少period
        invalid_data = {
            "wallet_address": "test_wallet_123",
            "chain": "sol"
        }
        result = validate_wallet_data(invalid_data)
        assert result is False
    
    def test_validate_wallet_data_invalid_types(self):
        """测试用例 4.3: 无效数据类型"""
        # 非字典类型
        result = validate_wallet_data("invalid_data")
        assert result is False
        
        # 钱包地址类型无效
        invalid_data = {
            "wallet_address": 123,
            "period": "all",
            "chain": "sol"
        }
        result = validate_wallet_data(invalid_data)
        assert result is False
    
    def test_validate_wallet_data_invalid_values(self):
        """测试用例 4.4: 无效字段值"""
        # 无效period
        invalid_data = {
            "wallet_address": "test_wallet_123",
            "period": "invalid_period",
            "chain": "sol"
        }
        result = validate_wallet_data(invalid_data)
        assert result is False
        
        # 无效chain
        invalid_data = {
            "wallet_address": "test_wallet_123",
            "period": "all",
            "chain": "eth"
        }
        result = validate_wallet_data(invalid_data)
        assert result is False
    
    def test_validate_wallet_data_numeric_fields(self):
        """测试用例 4.5: 数值字段验证"""
        # 有效数值字段
        valid_data = {
            "wallet_address": "test_wallet_123",
            "period": "all",
            "chain": "sol",
            "buy": 10,
            "sell": "8",  # 字符串数字也应该有效
            "win_rate": 0.6
        }
        result = validate_wallet_data(valid_data)
        assert result is True
        
        # 无效数值字段
        invalid_data = {
            "wallet_address": "test_wallet_123",
            "period": "all",
            "chain": "sol",
            "buy": "invalid_number"
        }
        result = validate_wallet_data(invalid_data)
        assert result is False
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.GmgnWalletStatsSpider')
    async def test_get_wallet_stats_multiple_periods_success(self, mock_spider_class, mock_wallet_stats):
        """测试用例 5.1: 成功获取多时间窗口数据"""
        # Mock爬虫实例
        mock_spider = AsyncMock()
        mock_spider.get_multiple_periods_stats.return_value = {
            "all": mock_wallet_stats,
            "7d": mock_wallet_stats,
            "1d": None  # 1天数据不存在
        }
        mock_spider.close.return_value = None
        mock_spider_class.return_value = mock_spider
        
        result = await get_wallet_stats_multiple_periods("test_wallet_123")
        
        # 验证结果
        assert result is not None
        assert len(result) == 3
        assert "all" in result
        assert "7d" in result
        assert "1d" in result
        assert result["all"]["wallet_address"] == "test_wallet_123"
        assert result["all"]["period"] == "all"
        assert result["1d"] is None
        
        # 验证调用
        mock_spider.get_multiple_periods_stats.assert_called_once_with(
            "test_wallet_123",
            periods=["all", "7d", "1d"]
        )
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.GmgnWalletStatsSpider')
    async def test_get_wallet_stats_multiple_periods_empty_address(self, mock_spider_class):
        """测试用例 5.2: 空钱包地址处理"""
        result = await get_wallet_stats_multiple_periods("")
        assert result is None
        
        result = await get_wallet_stats_multiple_periods(None)
        assert result is None
        
        # 验证爬虫未被创建
        mock_spider_class.assert_not_called()
    
    @patch('workflows.gmgn_kol_wallet_stats.handler.GmgnWalletStatsSpider')
    async def test_get_wallet_stats_multiple_periods_exception(self, mock_spider_class):
        """测试用例 5.3: 处理过程中异常"""
        # Mock爬虫抛出异常
        mock_spider = AsyncMock()
        mock_spider.get_multiple_periods_stats.side_effect = Exception("Network error")
        mock_spider.close.return_value = None
        mock_spider_class.return_value = mock_spider
        
        result = await get_wallet_stats_multiple_periods("test_wallet_123")
        
        # 验证返回None
        assert result is None 